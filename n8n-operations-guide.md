# n8n Operations Guide for Azure AKS

## 🎉 Deployment Complete!

Your n8n instance is successfully deployed on Azure AKS cluster `aks-n8n`.

### Access Information
- **URL**: http://************
- **Username**: admin
- **Password**: SecurePassword123!
- **Namespace**: n8n

## Quick Access Commands

### Check Status
```bash
# Check all n8n resources
kubectl get all -n n8n

# Check pod status
kubectl get pods -n n8n

# Check service and external IP
kubectl get service n8n-service -n n8n

# Check persistent volume
kubectl get pvc -n n8n
```

### View Logs
```bash
# View current logs
kubectl logs -n n8n deployment/n8n

# Follow logs in real-time
kubectl logs -n n8n deployment/n8n -f

# View last 50 lines
kubectl logs -n n8n deployment/n8n --tail=50
```

### Scaling Operations
```bash
# Scale to 2 replicas
kubectl scale deployment n8n -n n8n --replicas=2

# Scale back to 1 replica
kubectl scale deployment n8n -n n8n --replicas=1

# Check HPA status
kubectl get hpa -n n8n
```

## Management Operations

### Update n8n
```bash
# Update to latest version
kubectl set image deployment/n8n -n n8n n8n=n8nio/n8n:latest

# Update to specific version
kubectl set image deployment/n8n -n n8n n8n=n8nio/n8n:1.100.1

# Check rollout status
kubectl rollout status deployment/n8n -n n8n

# Rollback if needed
kubectl rollout undo deployment/n8n -n n8n
```

### Configuration Updates
```bash
# Edit ConfigMap
kubectl edit configmap n8n-config -n n8n

# Edit Secret (base64 encoded values)
kubectl edit secret n8n-auth -n n8n

# Restart deployment after config changes
kubectl rollout restart deployment/n8n -n n8n
```

### Resource Management
```bash
# Check resource usage
kubectl top pods -n n8n

# Check node resources
kubectl top nodes

# Describe deployment for detailed info
kubectl describe deployment n8n -n n8n
```

## Monitoring and Troubleshooting

### Health Checks
```bash
# Check pod health
kubectl get pods -n n8n -o wide

# Check events
kubectl get events -n n8n --sort-by='.lastTimestamp'

# Describe pod for detailed status
kubectl describe pod -n n8n -l app=n8n
```

### Common Issues and Solutions

#### 1. Pod Not Starting
```bash
# Check pod events
kubectl describe pod -n n8n -l app=n8n

# Check logs for errors
kubectl logs -n n8n -l app=n8n

# Common causes:
# - Image pull issues
# - Resource constraints
# - Volume mount problems
```

#### 2. Service Not Accessible
```bash
# Check service endpoints
kubectl get endpoints -n n8n

# Check LoadBalancer status
kubectl get service n8n-service -n n8n -o wide

# Test internal connectivity
kubectl exec -n n8n -it deployment/n8n -- curl localhost:5678/healthz
```

#### 3. Storage Issues
```bash
# Check PVC status
kubectl get pvc -n n8n

# Check storage class
kubectl get storageclass

# Describe PVC for events
kubectl describe pvc n8n-pvc -n n8n
```

### Performance Monitoring
```bash
# Monitor resource usage
kubectl top pods -n n8n

# Check HPA metrics
kubectl describe hpa n8n-hpa -n n8n

# View detailed metrics (if metrics-server is available)
kubectl get --raw /apis/metrics.k8s.io/v1beta1/namespaces/n8n/pods
```

## Security Operations

### Update Credentials
```bash
# Create new password (base64 encode first)
echo -n "NewSecurePassword456!" | base64

# Update secret
kubectl patch secret n8n-auth -n n8n -p='{"data":{"password":"TmV3U2VjdXJlUGFzc3dvcmQ0NTYh"}}'

# Restart deployment
kubectl rollout restart deployment/n8n -n n8n
```

### Network Security
```bash
# Check current LoadBalancer source ranges
kubectl get service n8n-service -n n8n -o yaml | grep -A5 loadBalancerSourceRanges

# Update to restrict access (edit the service)
kubectl edit service n8n-service -n n8n
```

## Backup and Recovery

### Data Backup
```bash
# Create backup of n8n data (requires access to the pod)
kubectl exec -n n8n deployment/n8n -- tar -czf /tmp/n8n-backup.tar.gz -C /home/<USER>/.n8n .

# Copy backup from pod
kubectl cp n8n/$(kubectl get pod -n n8n -l app=n8n -o jsonpath='{.items[0].metadata.name}'):/tmp/n8n-backup.tar.gz ./n8n-backup-$(date +%Y%m%d).tar.gz
```

### Configuration Backup
```bash
# Export all n8n resources
kubectl get all,configmap,secret,pvc -n n8n -o yaml > n8n-resources-backup.yaml

# Export just the deployment configuration
kubectl get deployment,service,configmap,secret,pvc -n n8n -o yaml > n8n-config-backup.yaml
```

## Cleanup Operations

### Remove n8n Deployment
```bash
# Delete all n8n resources
kubectl delete namespace n8n

# Or delete specific resources
kubectl delete -f n8n-deployment.yaml
```

### Cleanup Storage
```bash
# Note: PVCs are not automatically deleted with namespace
# Check for orphaned PVCs
kubectl get pvc --all-namespaces

# Delete specific PVC if needed
kubectl delete pvc n8n-pvc -n n8n
```

## Advanced Configuration

### Custom Domain Setup
1. Update the WEBHOOK_URL in ConfigMap
2. Set up Ingress controller
3. Configure DNS
4. Set up SSL/TLS certificates

### Database Integration
- n8n supports PostgreSQL, MySQL, SQLite
- Update environment variables in ConfigMap
- Create database connection secrets

### SMTP Configuration
- Add SMTP environment variables to ConfigMap
- Configure email notifications

## Support and Documentation

- **n8n Documentation**: https://docs.n8n.io/
- **Kubernetes Documentation**: https://kubernetes.io/docs/
- **Azure AKS Documentation**: https://docs.microsoft.com/en-us/azure/aks/

## Emergency Contacts and Procedures

### Quick Recovery
```bash
# If n8n is completely down, redeploy:
kubectl apply -f n8n-deployment.yaml

# If data is corrupted, restore from backup:
# 1. Delete current deployment
# 2. Restore PVC data
# 3. Redeploy n8n
```

---
**Last Updated**: $(date)
**Cluster**: aks-n8n
**Resource Group**: RG-SEA-EAPI-POC-001
**Subscription**: 3c6373fe-834d-4f88-bb56-539b8e02bd96
