# n8n Monitoring Script for Azure AKS
# This script provides a comprehensive overview of your n8n deployment

param(
    [switch]$Detailed,
    [switch]$Logs,
    [switch]$Watch,
    [int]$LogLines = 20
)

Write-Host "n8n Monitoring Dashboard" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host "Cluster: aks-n8n" -ForegroundColor Yellow
Write-Host "Namespace: n8n" -ForegroundColor Yellow
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Function to check if kubectl is available
function Test-Kubectl {
    try {
        kubectl version --client --short 2>$null | Out-Null
        return $true
    } catch {
        return $false
    }
}

# Function to get n8n status
function Get-N8nStatus {
    Write-Host "🔍 n8n Deployment Status" -ForegroundColor Blue
    Write-Host "------------------------" -ForegroundColor Blue
    
    try {
        # Get pod status
        $pods = kubectl get pods -n n8n -o json | ConvertFrom-Json
        if ($pods.items.Count -gt 0) {
            foreach ($pod in $pods.items) {
                $name = $pod.metadata.name
                $status = $pod.status.phase
                $ready = "$($pod.status.containerStatuses[0].ready)"
                $restarts = $pod.status.containerStatuses[0].restartCount
                $age = kubectl get pod $name -n n8n -o jsonpath='{.metadata.creationTimestamp}'
                
                $statusColor = switch ($status) {
                    "Running" { "Green" }
                    "Pending" { "Yellow" }
                    "Failed" { "Red" }
                    default { "White" }
                }
                
                Write-Host "  Pod: $name" -ForegroundColor White
                Write-Host "    Status: $status" -ForegroundColor $statusColor
                Write-Host "    Ready: $ready" -ForegroundColor $(if ($ready -eq "True") { "Green" } else { "Red" })
                Write-Host "    Restarts: $restarts" -ForegroundColor $(if ($restarts -eq 0) { "Green" } else { "Yellow" })
                Write-Host ""
            }
        } else {
            Write-Host "  ❌ No pods found" -ForegroundColor Red
        }
        
        # Get service status
        Write-Host "🌐 Service Status" -ForegroundColor Blue
        Write-Host "----------------" -ForegroundColor Blue
        $service = kubectl get service n8n-service -n n8n -o json | ConvertFrom-Json
        $externalIP = $service.status.loadBalancer.ingress[0].ip
        $port = $service.spec.ports[0].port
        
        Write-Host "  Service: n8n-service" -ForegroundColor White
        Write-Host "    Type: LoadBalancer" -ForegroundColor Green
        Write-Host "    External IP: $externalIP" -ForegroundColor Green
        Write-Host "    Port: $port" -ForegroundColor Green
        Write-Host "    URL: http://$externalIP" -ForegroundColor Cyan
        Write-Host ""
        
        # Get storage status
        Write-Host "💾 Storage Status" -ForegroundColor Blue
        Write-Host "----------------" -ForegroundColor Blue
        $pvc = kubectl get pvc n8n-pvc -n n8n -o json | ConvertFrom-Json
        $status = $pvc.status.phase
        $capacity = $pvc.status.capacity.storage
        $storageClass = $pvc.spec.storageClassName
        
        Write-Host "  PVC: n8n-pvc" -ForegroundColor White
        Write-Host "    Status: $status" -ForegroundColor $(if ($status -eq "Bound") { "Green" } else { "Red" })
        Write-Host "    Capacity: $capacity" -ForegroundColor Green
        Write-Host "    Storage Class: $storageClass" -ForegroundColor Green
        Write-Host ""
        
    } catch {
        Write-Host "❌ Error getting n8n status: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to get resource usage
function Get-ResourceUsage {
    Write-Host "📊 Resource Usage" -ForegroundColor Blue
    Write-Host "----------------" -ForegroundColor Blue
    
    try {
        $usage = kubectl top pods -n n8n --no-headers 2>$null
        if ($usage) {
            Write-Host "  $usage" -ForegroundColor White
        } else {
            Write-Host "  ⚠️  Resource metrics not available (metrics-server may not be installed)" -ForegroundColor Yellow
        }
        Write-Host ""
        
        # Get HPA status
        Write-Host "🔄 Auto-scaling Status" -ForegroundColor Blue
        Write-Host "---------------------" -ForegroundColor Blue
        $hpa = kubectl get hpa n8n-hpa -n n8n -o json 2>$null | ConvertFrom-Json
        if ($hpa) {
            $currentReplicas = $hpa.status.currentReplicas
            $desiredReplicas = $hpa.status.desiredReplicas
            $minReplicas = $hpa.spec.minReplicas
            $maxReplicas = $hpa.spec.maxReplicas
            
            Write-Host "  Current Replicas: $currentReplicas" -ForegroundColor White
            Write-Host "  Desired Replicas: $desiredReplicas" -ForegroundColor White
            Write-Host "  Min/Max Replicas: $minReplicas/$maxReplicas" -ForegroundColor White
        } else {
            Write-Host "  ⚠️  HPA not found or not available" -ForegroundColor Yellow
        }
        Write-Host ""
        
    } catch {
        Write-Host "❌ Error getting resource usage: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to show recent events
function Get-RecentEvents {
    Write-Host "📋 Recent Events" -ForegroundColor Blue
    Write-Host "---------------" -ForegroundColor Blue
    
    try {
        $events = kubectl get events -n n8n --sort-by='.lastTimestamp' --no-headers | Select-Object -Last 5
        if ($events) {
            foreach ($event in $events) {
                Write-Host "  $event" -ForegroundColor Gray
            }
        } else {
            Write-Host "  ℹ️  No recent events" -ForegroundColor Gray
        }
        Write-Host ""
    } catch {
        Write-Host "❌ Error getting events: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to show logs
function Get-N8nLogs {
    Write-Host "📝 n8n Logs (Last $LogLines lines)" -ForegroundColor Blue
    Write-Host "$(('-' * (20 + $LogLines.ToString().Length)))" -ForegroundColor Blue
    
    try {
        $logs = kubectl logs -n n8n deployment/n8n --tail=$LogLines
        if ($logs) {
            Write-Host $logs -ForegroundColor White
        } else {
            Write-Host "  ℹ️  No logs available" -ForegroundColor Gray
        }
        Write-Host ""
    } catch {
        Write-Host "❌ Error getting logs: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to test n8n connectivity
function Test-N8nConnectivity {
    Write-Host "🔗 Connectivity Test" -ForegroundColor Blue
    Write-Host "-------------------" -ForegroundColor Blue
    
    try {
        $service = kubectl get service n8n-service -n n8n -o json | ConvertFrom-Json
        $externalIP = $service.status.loadBalancer.ingress[0].ip
        
        if ($externalIP) {
            Write-Host "  Testing connection to http://$externalIP..." -ForegroundColor White
            
            try {
                $response = Invoke-WebRequest -Uri "http://$externalIP" -TimeoutSec 10 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    Write-Host "  ✅ n8n is accessible!" -ForegroundColor Green
                } else {
                    Write-Host "  ⚠️  Received status code: $($response.StatusCode)" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "  ❌ Connection failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "  ⚠️  External IP not available yet" -ForegroundColor Yellow
        }
        Write-Host ""
    } catch {
        Write-Host "❌ Error testing connectivity: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution
if (-not (Test-Kubectl)) {
    Write-Host "❌ kubectl is not available. Please ensure kubectl is installed and configured." -ForegroundColor Red
    exit 1
}

# Check if n8n namespace exists
try {
    kubectl get namespace n8n 2>$null | Out-Null
} catch {
    Write-Host "❌ n8n namespace not found. Is n8n deployed?" -ForegroundColor Red
    exit 1
}

# Execute monitoring functions
Get-N8nStatus
Get-ResourceUsage

if ($Detailed) {
    Get-RecentEvents
    Test-N8nConnectivity
}

if ($Logs) {
    Get-N8nLogs
}

if ($Watch) {
    Write-Host "👀 Watching n8n status (Press Ctrl+C to stop)..." -ForegroundColor Cyan
    Write-Host ""
    
    while ($true) {
        Clear-Host
        Write-Host "n8n Monitoring Dashboard - Watch Mode" -ForegroundColor Green
        Write-Host "=====================================" -ForegroundColor Green
        Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Gray
        Write-Host ""
        
        Get-N8nStatus
        Get-ResourceUsage
        
        Start-Sleep -Seconds 10
    }
}

Write-Host "✅ Monitoring complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Usage examples:" -ForegroundColor Yellow
Write-Host "  .\monitor-n8n.ps1                    # Basic status"
Write-Host "  .\monitor-n8n.ps1 -Detailed          # Detailed status with events and connectivity test"
Write-Host "  .\monitor-n8n.ps1 -Logs              # Include recent logs"
Write-Host "  .\monitor-n8n.ps1 -Watch             # Watch mode (refreshes every 10 seconds)"
Write-Host "  .\monitor-n8n.ps1 -Logs -LogLines 50 # Show 50 lines of logs"
