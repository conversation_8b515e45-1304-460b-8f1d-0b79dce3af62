# Azure AKS Connection Script
# This script helps you connect to your Azure Kubernetes Service cluster

Write-Host "Azure AKS Connection Script" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green

# Your cluster details
$subscriptionId = "3c6373fe-834d-4f88-bb56-539b8e02bd96"
$resourceGroup = "RG-SEA-EAPI-POC-001"
$clusterName = "aks-n8n"

Write-Host "Cluster Details:" -ForegroundColor Yellow
Write-Host "  Subscription ID: $subscriptionId"
Write-Host "  Resource Group: $resourceGroup"
Write-Host "  Cluster Name: $clusterName"
Write-Host ""

# Check if Azure CLI is installed
Write-Host "Checking Azure CLI installation..." -ForegroundColor Blue
try {
    $azVersion = az --version 2>$null
    if ($azVersion) {
        Write-Host "✓ Azure CLI is installed" -ForegroundColor Green
    } else {
        throw "Azure CLI not found"
    }
} catch {
    Write-Host "✗ Azure CLI is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Azure CLI from: https://aka.ms/installazurecliwindows" -ForegroundColor Yellow
    Write-Host "After installation, restart PowerShell and run this script again." -ForegroundColor Yellow
    exit 1
}

# Check if kubectl is available
Write-Host "Checking kubectl installation..." -ForegroundColor Blue
try {
    if (Test-Path ".\kubectl.exe") {
        Write-Host "✓ kubectl found in current directory" -ForegroundColor Green
        $kubectlPath = ".\kubectl.exe"
    } elseif (Get-Command kubectl -ErrorAction SilentlyContinue) {
        Write-Host "✓ kubectl found in PATH" -ForegroundColor Green
        $kubectlPath = "kubectl"
    } else {
        throw "kubectl not found"
    }
} catch {
    Write-Host "✗ kubectl is not available" -ForegroundColor Red
    Write-Host "kubectl.exe should be in the current directory or in your PATH" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Step 1: Login to Azure" -ForegroundColor Blue
Write-Host "Running: az login" -ForegroundColor Gray
try {
    az login
    if ($LASTEXITCODE -ne 0) {
        throw "Azure login failed"
    }
    Write-Host "✓ Successfully logged in to Azure" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to login to Azure" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Step 2: Set Azure subscription" -ForegroundColor Blue
Write-Host "Running: az account set --subscription $subscriptionId" -ForegroundColor Gray
try {
    az account set --subscription $subscriptionId
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to set subscription"
    }
    Write-Host "✓ Successfully set subscription" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to set subscription" -ForegroundColor Red
    Write-Host "Please verify you have access to subscription: $subscriptionId" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Step 3: Get AKS credentials" -ForegroundColor Blue
Write-Host "Running: az aks get-credentials --resource-group $resourceGroup --name $clusterName" -ForegroundColor Gray
try {
    az aks get-credentials --resource-group $resourceGroup --name $clusterName
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to get AKS credentials"
    }
    Write-Host "✓ Successfully retrieved AKS credentials" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to get AKS credentials" -ForegroundColor Red
    Write-Host "Please verify the cluster exists and you have access to it" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Step 4: Verify connection" -ForegroundColor Blue
Write-Host "Running: $kubectlPath get nodes" -ForegroundColor Gray
try {
    & $kubectlPath get nodes
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to get nodes"
    }
    Write-Host "✓ Successfully connected to AKS cluster!" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to connect to cluster" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 Connection successful!" -ForegroundColor Green
Write-Host "You can now use kubectl to manage your AKS cluster." -ForegroundColor Green
Write-Host ""
Write-Host "Common commands:" -ForegroundColor Yellow
Write-Host "  $kubectlPath get nodes                    # List cluster nodes"
Write-Host "  $kubectlPath get pods --all-namespaces   # List all pods"
Write-Host "  $kubectlPath get services                # List services"
Write-Host "  $kubectlPath get namespaces              # List namespaces"
