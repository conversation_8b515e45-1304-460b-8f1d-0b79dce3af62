# Simple n8n Status Check Script
Write-Host "n8n Status Check" -ForegroundColor Green
Write-Host "================" -ForegroundColor Green
Write-Host ""

# Check pods
Write-Host "Pod Status:" -ForegroundColor Blue
kubectl get pods -n n8n
Write-Host ""

# Check service
Write-Host "Service Status:" -ForegroundColor Blue
kubectl get service n8n-service -n n8n
Write-Host ""

# Check PVC
Write-Host "Storage Status:" -ForegroundColor Blue
kubectl get pvc -n n8n
Write-Host ""

# Get external IP
Write-Host "Access Information:" -ForegroundColor Blue
$externalIP = kubectl get service n8n-service -n n8n -o jsonpath='{.status.loadBalancer.ingress[0].ip}'
if ($externalIP) {
    Write-Host "URL: http://$externalIP" -ForegroundColor Green
    Write-Host "Username: admin" -ForegroundColor Green
    Write-Host "Password: SecurePassword123!" -ForegroundColor Green
} else {
    Write-Host "External IP not yet assigned" -ForegroundColor Yellow
}
Write-Host ""

# Show recent logs
Write-Host "Recent Logs:" -ForegroundColor Blue
kubectl logs -n n8n deployment/n8n --tail=5
